"use client";

import React, { useEffect } from "react";
import PageLayout from "@/components/layout/PageLayout";
import AOS from "aos";
import "aos/dist/aos.css";

const Company = () => {

  // Initialize AOS animation library
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: false,
      mirror: true,
    });
  }, []);

  // Add text shadow style and Open Sans font
  const textShadowStyle = {
    textShadow: '1px 1px 1px rgba(0,0,0,0.1)',
    fontFamily: '"Open Sans", sans-serif'
  };
    return (
    <PageLayout
      title="ABOUT US"
      subtitle="Power & Energy Management since 1984"
      category="about"
      textColor="text-blue-800"
    >
      {/* Full width container with complete page utilization */}
      <div className="w-full min-h-screen px-2 sm:px-4 lg:px-6 xl:px-8 py-2 sm:py-4 lg:py-6 font-['Open_Sans']">
        {/* Main Content - Enhanced responsive grid with complete width utilization */}
        <div className="w-full grid grid-cols-1 gap-3 sm:gap-4 md:gap-6 lg:grid-cols-2 lg:gap-8 xl:gap-10">
          {/* Left Column - Company Information */}
          <div className="w-full space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8" data-aos="fade-right" data-aos-delay="100">
            <div className="w-full">
              <h2 className="w-full text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold mb-2 sm:mb-3 md:mb-4 lg:mb-6 text-blue-700 bg-gradient-to-r from-blue-100 to-blue-200 py-1 sm:py-1.5 md:py-2 lg:py-3 px-2 sm:px-3 md:px-4 rounded-lg shadow-sm text-center" style={textShadowStyle}>
                Our Story
              </h2>
              <p className="w-full text-blue-900 mb-1.5 sm:mb-2 md:mb-3 lg:mb-4 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-up" data-aos-delay="100" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                Atandra Energy Pvt. Ltd., headquartered in Chennai, draws
                upon a rich foundation of more than 39+ years of expertise in
                the realm of Power & Energy Management.
              </p>
              <p className="w-full text-blue-900 mb-1.5 sm:mb-2 md:mb-3 lg:mb-4 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-up" data-aos-delay="200" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                We offer solutions to industrial & commercial
                establishments under our popular brand <span className="font-bold text-yellow-700 bg-yellow-100 px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm md:text-base lg:text-lg">KRYKARD</span>. With over
                5,00,000 installations of Power Conditioners & over 1,50,000
                installations of Portable & Panel Load Managers, KRYKARD is
                one of the leading brands in Power Conditioning & Energy
                Management.
              </p>
              <p className="w-full text-blue-900 mb-1.5 sm:mb-2 md:mb-3 lg:mb-4 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-up" data-aos-delay="300" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                Our Servo Stabilizers & Transformers have obtained <span className="font-bold text-yellow-700 bg-yellow-100 px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm md:text-base lg:text-lg">CE
                certification</span>, providing our customers with the assurance
                that these products adhere to rigorous global health, safety,
                & environmental protection standards.
              </p>
            </div>
            <div className="w-full" data-aos="fade-up" data-aos-delay="300">
              <h3 className="w-full text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-semibold mb-1.5 sm:mb-2 md:mb-3 lg:mb-4 text-green-700 bg-gradient-to-r from-green-100 to-green-200 py-1 sm:py-1.5 md:py-2 lg:py-3 px-2 sm:px-3 md:px-4 rounded-lg shadow-sm text-center" style={textShadowStyle}>
                Our Facilities
              </h3>
              <p className="w-full text-green-900 mb-1 sm:mb-1.5 md:mb-2 font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-left" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                We have the following facilities:
              </p>
              <ul className="w-full list-disc pl-2 sm:pl-3 md:pl-4 lg:pl-6 text-green-900 space-y-1 sm:space-y-1.5 md:space-y-2">
                <li className="leading-relaxed text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-right" data-aos-delay="400" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                  <span className="font-bold text-green-800 underline decoration-yellow-500 decoration-1 sm:decoration-2 underline-offset-2">R&D department</span> for Power Electronics & Electro-magnetics.
                </li>
                <li className="leading-relaxed text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-right" data-aos-delay="600" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                  <span className="font-bold text-green-800 underline decoration-yellow-500 decoration-1 sm:decoration-2 underline-offset-2">Software Development department</span> for energy management software & Industry 4.0 solutions.
                </li>
              </ul>
            </div>
          </div>

          {/* Right Column - Achievements & Stats */}
          <div className="w-full space-y-3 sm:space-y-4 md:space-y-6 lg:space-y-8" data-aos="fade-left" data-aos-delay="200">
            {/* Achievements Grid */}
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 md:gap-4 lg:gap-6">
              {/* Achievement 1 */}
              <div
                className="w-full bg-gradient-to-r from-yellow-50 via-white to-yellow-50 rounded-lg p-2 sm:p-3 md:p-4 lg:p-6 text-center flex flex-col items-center shadow-md relative overflow-hidden border border-yellow-100"
                data-aos="zoom-in"
                data-aos-delay="100"
              >
                <div className="mb-2 sm:mb-3 md:mb-5 relative w-full">
                  <div className="flex items-center justify-center w-full">
                    {/* Left laurel wreath */}
                    <img
                      src="/images/icons/laurel-wreath.svg"
                      alt="Left laurel wreath"
                      className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 lg:h-16 lg:w-16 absolute -left-1 top-1/2 transform -translate-y-1/2"
                      style={{ transform: "translate(-30%, -50%) scaleX(-1)" }}
                    />

                    {/* Number 1 */}
                    <div className="h-12 w-12 sm:h-16 sm:w-16 md:h-20 md:w-20 lg:h-28 lg:w-28 mx-auto flex items-center justify-center bg-gradient-to-b from-yellow-300 to-amber-600 rounded-full z-10 shadow-md">
                      <span className="text-sm sm:text-lg md:text-xl lg:text-4xl font-bold text-white drop-shadow-md">NO. 1</span>
                    </div>

                    {/* Right laurel wreath */}
                    <img
                      src="/images/icons/laurel-wreath.svg"
                      alt="Right laurel wreath"
                      className="h-6 w-6 sm:h-8 sm:w-8 md:h-10 md:w-10 lg:h-16 lg:w-16 absolute -right-1 top-1/2 transform -translate-y-1/2"
                      style={{ transform: "translate(30%, -50%)" }}
                    />
                  </div>
                </div>
                <h3 className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-bold text-indigo-900 mb-0.5 sm:mb-1 text-center" style={{ fontFamily: '"Open Sans", sans-serif' }}>INDIA'S NO. 1</h3>
                <p className="font-semibold text-green-900 text-xs sm:text-sm md:text-base lg:text-lg text-center" style={{ fontFamily: '"Open Sans", sans-serif' }}>MANUFACTURER OF SERVO STABILISERS</p>
              </div>

              {/* Achievement 2 - 100+ SERVICE CENTERS */}
              <div
                className="w-full bg-white p-2 sm:p-3 md:p-4 lg:p-6 rounded-lg shadow-md border-l-4 border-l-yellow-500 hover:shadow-xl transition-all duration-300 group"
                data-aos="fade-up-right"
                data-aos-delay="200"
              >
                <div className="flex flex-col items-center w-full">
                  <div className="p-1 sm:p-1.5 md:p-2 lg:p-4 bg-yellow-50 rounded-lg mb-1.5 sm:mb-2 md:mb-3 group-hover:bg-yellow-100 transition-colors duration-300 flex items-center justify-center">
                    <svg className="h-4 w-4 sm:h-6 sm:w-6 md:h-8 md:w-8 lg:h-16 lg:w-16 text-yellow-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.25">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div className="text-center mt-0.5 sm:mt-1 w-full">
                    <h3 className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium text-gray-700 group-hover:text-yellow-700 transition-colors duration-300 tracking-wide text-center" style={{ fontFamily: '"Open Sans", sans-serif' }}>100+ SERVICE CENTERS</h3>
                    <div className="h-0.5 w-6 sm:w-8 md:w-12 lg:w-16 bg-yellow-500 mt-0.5 sm:mt-1 md:mt-2 mx-auto group-hover:w-8 sm:group-hover:w-12 md:group-hover:w-20 lg:group-hover:w-24 transition-all duration-500"></div>
                  </div>
                </div>
              </div>

              {/* Achievement 3 */}
              <div
                className="w-full bg-white p-2 sm:p-3 md:p-4 lg:p-6 rounded-lg shadow-md border-l-4 border-l-green-500 hover:shadow-lg transition-all duration-300"
                data-aos="fade-up-left"
                data-aos-delay="300"
              >
                <div className="flex items-center mb-1.5 sm:mb-2 md:mb-4 w-full">
                  <div className="p-1 sm:p-1.5 md:p-2 lg:p-3 bg-green-100 rounded-full mr-1.5 sm:mr-2 md:mr-3 lg:mr-4">
                    <svg className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 lg:h-6 lg:w-6 text-green-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h3 className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-bold text-green-700 text-left" style={{ fontFamily: '"Open Sans", sans-serif' }}>PREFERRED SUPPLIER OF</h3>
                </div>
                <p className="font-semibold text-green-800 ml-6 sm:ml-8 md:ml-10 lg:ml-16 text-xs sm:text-sm md:text-base lg:text-lg text-left" style={{ fontFamily: '"Open Sans", sans-serif' }}>LARGE CORPORATES & OEM'S</p>
              </div>

              {/* Achievement 4 */}
              <div
                className="w-full bg-white p-2 sm:p-3 md:p-4 lg:p-6 rounded-lg shadow-md border-l-4 border-l-blue-500 hover:shadow-lg transition-all duration-300"
                data-aos="fade-up-right"
                data-aos-delay="400"
              >
                <div className="flex items-center mb-1.5 sm:mb-2 md:mb-4 w-full">
                  <div className="p-1 sm:p-1.5 md:p-2 lg:p-3 bg-blue-100 rounded-full mr-1.5 sm:mr-2 md:mr-3 lg:mr-4">
                    <svg className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 lg:h-6 lg:w-6 text-blue-700" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <circle cx="12" cy="12" r="10" strokeWidth="2" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 11l6 0" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11l-3 5.196" />
                    </svg>
                  </div>
                  <h3 className="text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-bold text-blue-700 text-left" style={{ fontFamily: '"Open Sans", sans-serif' }}>CE CERTIFIED PRODUCTS</h3>
                </div>
              </div>

              {/* Achievement 5 - Experience */}
              <div
                className="w-full bg-gradient-to-r from-blue-100 via-yellow-100 to-green-100 rounded-lg p-2 sm:p-3 md:p-4 lg:p-6 text-center flex flex-col items-center col-span-1 sm:col-span-2 shadow-md"
                data-aos="zoom-in-up"
                data-aos-delay="500"
              >
                <div className="mb-1.5 sm:mb-2 md:mb-4 relative">
                  <div className="rounded-full bg-blue-700 h-10 w-10 sm:h-12 sm:w-12 md:h-16 md:w-16 lg:h-24 lg:w-24 flex items-center justify-center shadow-md">
                    <span className="text-sm sm:text-lg md:text-xl lg:text-3xl font-bold text-white">39+</span>
                  </div>
                </div>
                <h3 className="text-xs sm:text-sm md:text-base lg:text-xl xl:text-2xl font-bold text-blue-800 text-center" style={{ fontFamily: '"Open Sans", sans-serif' }}>39+ YEARS EXPERIENCE</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Certifications and Commitment Sections - Enhanced responsive layout */}
        <div className="w-full my-4 sm:my-6 md:my-8 lg:my-12 xl:my-20">
          <div className="w-full grid grid-cols-1 gap-3 sm:gap-4 md:gap-6 lg:grid-cols-2 lg:gap-8 xl:gap-10">
            {/* Left Section - Certifications */}
            <div
              className="w-full transform transition-all duration-700 hover:scale-105"
              data-aos="fade-right"
              data-aos-delay="100"
              data-aos-offset="200"
            >
              <div className="relative overflow-hidden bg-gradient-to-r from-yellow-200 via-yellow-100 to-yellow-200 rounded-xl shadow-lg border border-yellow-300 h-full w-full">
                {/* Animated background elements */}
                <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20">
                  <div className="absolute top-3 left-3 sm:top-5 sm:left-5 w-12 h-12 sm:w-20 sm:h-20 rounded-full bg-yellow-400 animate-pulse" data-aos="zoom-in" data-aos-delay="200"></div>
                  <div className="absolute bottom-6 right-6 sm:bottom-10 sm:right-10 w-20 h-20 sm:w-32 sm:h-32 rounded-full bg-yellow-300 animate-ping opacity-30" data-aos="zoom-in" data-aos-delay="400"></div>
                  <div className="absolute top-1/2 left-1/2 w-24 h-24 sm:w-40 sm:h-40 rounded-full bg-yellow-400 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="600"></div>
                </div>

                {/* Content */}
                <div className="relative z-10 p-2 sm:p-4 md:p-6 lg:p-8 w-full">
                  {/* Header with shine effect */}
                  <div className="relative overflow-hidden mb-2 sm:mb-4 md:mb-6 w-full">
                    <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-yellow-800 text-center" style={textShadowStyle}>
                      Certifications
                    </h2>
                    <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-yellow-100 to-transparent skew-x-12 animate-shine"></div>
                  </div>
                  <p className="text-yellow-800 mb-2 sm:mb-4 md:mb-6 font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-center" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                    Our organization has the following certifications:
                  </p>                  {/* Certification badges with hover effects */}
                  <div className="w-full grid grid-cols-2 gap-1 sm:gap-1.5 md:gap-2 lg:gap-4 mt-1.5 sm:mt-2 md:mt-4">
                    <div
                      className="w-full bg-blue-100 rounded-lg shadow-md p-1 sm:p-1.5 md:p-2 lg:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                      data-aos="flip-left"
                      data-aos-delay="100"
                    >
                      <p className="text-blue-900 font-bold text-center text-2xs sm:text-xs md:text-sm lg:text-base xl:text-lg" style={{ fontFamily: '"Open Sans", sans-serif' }}>ISO 9001:2015</p>
                    </div>
                    <div
                      className="w-full bg-blue-100 rounded-lg shadow-md p-1 sm:p-1.5 md:p-2 lg:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                      data-aos="flip-right"
                      data-aos-delay="200"
                    >
                      <p className="text-blue-900 font-bold text-center text-2xs sm:text-xs md:text-sm lg:text-base xl:text-lg" style={{ fontFamily: '"Open Sans", sans-serif' }}>14001 – 2015</p>
                    </div>
                    <div
                      className="w-full bg-blue-100 rounded-lg shadow-md p-1 sm:p-1.5 md:p-2 lg:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                      data-aos="flip-left"
                      data-aos-delay="300"
                    >
                      <p className="text-blue-900 font-bold text-center text-2xs sm:text-xs md:text-sm lg:text-base xl:text-lg" style={{ fontFamily: '"Open Sans", sans-serif' }}>45001 – 2018</p>
                    </div>
                    <div
                      className="w-full bg-blue-100 rounded-lg shadow-md p-1 sm:p-1.5 md:p-2 lg:p-3 hover:shadow-lg transition-all duration-300 hover:bg-blue-50 hover:-translate-y-1 border-l-4 border-blue-600"
                      data-aos="flip-right"
                      data-aos-delay="400"
                    >
                      <p className="text-blue-900 font-bold text-center text-2xs sm:text-xs md:text-sm lg:text-base xl:text-lg" style={{ fontFamily: '"Open Sans", sans-serif' }}>50001</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section - Our Commitment */}
            <div
              className="w-full transform transition-all duration-700 hover:scale-105"
              data-aos="fade-left"
              data-aos-delay="300"
              data-aos-offset="200"
            >
              <div className="relative overflow-hidden bg-gradient-to-r from-blue-200 via-blue-100 to-blue-200 rounded-xl shadow-lg border border-blue-300 h-full w-full">
                {/* Animated background elements */}
                <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-20">
                  <div className="absolute top-6 right-6 sm:top-10 sm:right-10 w-16 h-16 sm:w-24 sm:h-24 rounded-full bg-blue-400 animate-pulse" data-aos="zoom-in" data-aos-delay="300"></div>
                  <div className="absolute bottom-3 left-3 sm:bottom-5 sm:left-5 w-20 h-20 sm:w-28 sm:h-28 rounded-full bg-blue-300 animate-ping opacity-30" data-aos="zoom-in" data-aos-delay="500"></div>
                  <div className="absolute top-1/2 left-1/2 w-24 h-24 sm:w-40 sm:h-40 rounded-full bg-blue-400 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="700"></div>
                </div>

                {/* Content */}
                <div className="relative z-10 p-2 sm:p-4 md:p-6 lg:p-8 w-full">
                  {/* Header with shine effect */}
                  <div className="relative overflow-hidden mb-2 sm:mb-4 md:mb-6 w-full">
                    <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-blue-800 text-center" style={textShadowStyle}>
                      Our Commitment
                    </h2>
                    <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-blue-100 to-transparent skew-x-12 animate-shine"></div>
                  </div>
                  <div className="w-full space-y-2 sm:space-y-3 md:space-y-4 lg:space-y-6">
                    <p className="text-blue-900 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-up" data-aos-delay="100" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                      State-of-the-art facilities empower us to address the requirements of Indian
                      industries <span className="relative inline-block">
                        <span className="relative z-10 font-bold text-blue-900 bg-blue-200 px-1 sm:px-1.5 md:px-2 lg:px-3 py-0.5 sm:py-1 rounded text-xs sm:text-sm md:text-base lg:text-lg">
                          comprehensively, effectively & efficiently
                        </span>
                        <span className="absolute inset-0 bg-blue-300 rounded animate-pulse opacity-30"></span>
                      </span>, ensuring they derive maximum
                      benefits from the power conditioning & energy management solutions we provide.
                    </p>
                    <p className="text-blue-900 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-justify" data-aos="fade-up" data-aos-delay="300" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                      With a taskforce of around <span className="relative inline-block">
                        <span className="relative z-10 font-bold text-yellow-700 bg-yellow-200 px-1 sm:px-1.5 md:px-2 lg:px-3 py-0.5 sm:py-1 rounded text-xs sm:text-sm md:text-base lg:text-lg">
                          500+ employees
                        </span>
                        <span className="absolute inset-0 bg-yellow-300 rounded animate-pulse opacity-30"></span>
                      </span> & an extensive
                      network of sales & service branches nationwide, we are well-equipped to seamlessly reach
                      out to our customers & fulfil their needs.
                    </p>
                  </div>                  {/* Decorative elements */}
                  <div className="mt-3 sm:mt-4 md:mt-6 w-full grid grid-cols-4 gap-1.5 sm:gap-2">
                    <div className="h-0.5 sm:h-1 bg-blue-400 rounded animate-pulse" data-aos="fade-right" data-aos-delay="100"></div>
                    <div className="h-0.5 sm:h-1 bg-blue-500 rounded animate-pulse delay-100" data-aos="fade-right" data-aos-delay="200"></div>
                    <div className="h-0.5 sm:h-1 bg-blue-600 rounded animate-pulse delay-200" data-aos="fade-right" data-aos-delay="300"></div>
                    <div className="h-0.5 sm:h-1 bg-blue-700 rounded animate-pulse delay-300" data-aos="fade-right" data-aos-delay="400"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional keyframes for new animations */}
        <style dangerouslySetInnerHTML={{ __html: `
          @keyframes float-slow {
            0%, 100% {
              transform: translate(0, 0);
            }
            50% {
              transform: translate(10px, -15px);
            }
          }

          .animate-float-slow {
            animation: float-slow 8s ease-in-out infinite;
          }

          .bg-grid-pattern {
            background-image: linear-gradient(to right, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
          }

          /* Custom text size for very small screens */
          .text-2xs {
            font-size: 0.65rem;
            line-height: 1rem;
          }
        ` }} />        {/* Need More Information Section - Enhanced responsive layout */}
        <div className="w-full my-4 sm:my-6 md:my-8 lg:my-12 xl:my-20">
          <div
            className="relative overflow-hidden bg-gradient-to-r from-blue-50 via-blue-100 to-green-50 rounded-xl shadow-lg p-2 sm:p-3 md:p-5 lg:p-8 xl:p-10 border border-blue-200 w-full"
            data-aos="fade-up"
            data-aos-offset="200"
            data-aos-duration="1000"
          >
            {/* Animated background elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden opacity-10">
              <div className="absolute top-6 right-6 sm:top-10 sm:right-10 w-16 h-16 sm:w-24 sm:h-24 rounded-full bg-blue-400 animate-pulse" data-aos="fade-down-left" data-aos-delay="200"></div>
              <div className="absolute bottom-6 left-6 sm:bottom-10 sm:left-10 w-20 h-20 sm:w-32 sm:h-32 rounded-full bg-green-300 animate-ping opacity-30" data-aos="fade-up-right" data-aos-delay="400"></div>
              <div className="absolute top-1/2 left-1/2 w-24 h-24 sm:w-40 sm:h-40 rounded-full bg-yellow-300 -translate-x-1/2 -translate-y-1/2 opacity-20" data-aos="zoom-in" data-aos-delay="600"></div>
            </div>

            {/* Content */}
            <div className="relative z-10 text-center w-full max-w-4xl mx-auto">
              {/* Header with shine effect */}
              <div className="relative overflow-hidden mb-2 sm:mb-3 md:mb-4 lg:mb-6 w-full">
                <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-blue-700 mb-1.5 sm:mb-2 md:mb-4 lg:mb-6" style={textShadowStyle}>
                  Need More Information?
                </h2>
                <div className="absolute top-0 -inset-x-20 h-full w-1/3 bg-gradient-to-r from-transparent via-blue-100 to-transparent skew-x-12 animate-shine"></div>
              </div>
              <p className="text-blue-900 mb-3 sm:mb-4 md:mb-6 lg:mb-8 xl:mb-10 leading-relaxed font-medium text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl text-center" data-aos="fade-up" data-aos-delay="200" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                Our team of experts is ready to help you with product specifications, custom solutions,
                pricing, and any other details you need about the <span className="font-bold text-yellow-700 bg-yellow-100 px-1 sm:px-2 py-0.5 sm:py-1 rounded text-xs sm:text-sm md:text-base lg:text-lg">KRYKARD</span> Static Voltage Regulator.
              </p>

              {/* Contact button with animation - blue/green gradient to match page */}
              <div className="relative inline-block group w-full sm:w-auto" data-aos="zoom-in" data-aos-delay="400">
                {/* Button glow effect */}
                <div className="absolute -inset-1 bg-gradient-to-r from-blue-400 to-green-500 rounded-lg blur opacity-30 group-hover:opacity-80 transition duration-500"></div>
                <a href="/contact/sales" className="relative inline-flex items-center justify-center px-2 sm:px-3 md:px-4 lg:px-6 xl:px-8 py-1.5 sm:py-2 md:py-3 lg:py-4 text-xs sm:text-sm md:text-base lg:text-lg xl:text-xl font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform group-hover:scale-105 w-full sm:w-auto" style={{ fontFamily: '"Open Sans", sans-serif' }}>
                  Contact Our Experts
                  <svg className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 ml-1 sm:ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Add the animations */}
        <style dangerouslySetInnerHTML={{ __html: `
          @keyframes shine {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          @keyframes pulse {
            0%, 100% {
              opacity: 0.2;
            }
            50% {
              opacity: 0.5;
            }
          }

          @keyframes ping {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            75%, 100% {
              transform: scale(1.5);
              opacity: 0;
            }
          }

          @keyframes float {
            0%, 100% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-10px);
            }
          }

          @keyframes car-move {
            0% {
              left: 0%;
              top: 600px;
            }
            10% {
              left: 15%;
              top: 550px;
            }
            20% {
              left: 25%;
              top: 500px;
            }
            30% {
              left: 35%;
              top: 400px;
            }
            40% {
              left: 45%;
              top: 300px;
            }
            50% {
              left: 55%;
              top: 350px;
            }
            60% {
              left: 65%;
              top: 400px;
            }
            70% {
              left: 75%;
              top: 300px;
            }
            80% {
              left: 85%;
              top: 200px;
            }
            90% {
              left: 95%;
              top: 150px;
            }
            100% {
              left: 100%;
              top: 200px;
            }
          }

          .animate-shine {
            animation: shine 3s infinite;
          }

          .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
          }

          .animate-ping {
            animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
          }

          .animate-float {
            animation: float 3s ease-in-out infinite;
          }

          .animate-car-move {
            animation: car-move 20s linear infinite;
          }

          .delay-100 {
            animation-delay: 100ms;
          }

          .delay-200 {
            animation-delay: 200ms;
          }

          .delay-300 {
            animation-delay: 300ms;
          }
        ` }} />
      </div>
    </PageLayout>
  );
};

export default Company;